'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import {
  ArrowLeft,
  Calculator,
  Mail,
  User,
  Heart,
  Stethoscope,
  CheckCircle,
  AlertTriangle,
  Info,
  Target
} from 'lucide-react';
import Link from 'next/link';
import { assessment<PERSON>pi, invitationApi } from '@/lib/api';

// Finnish Diabetes Risk Score Schema
const assessmentSchema = z.object({
  // Patient Information
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().min(10, 'Please enter a valid phone number'),

  // Finnish Diabetes Risk Score Questions
  age: z.string(),
  bmi: z.string(),
  waistCircumference: z.string(),
  physicalActivity: z.string(),
  vegetableConsumption: z.string(),
  bloodPressureMedication: z.string(),
  highGlucoseHistory: z.string(),
  familyDiabetesHistory: z.string(),

  // Additional notes
  notes: z.string().optional(),
});

type AssessmentForm = z.infer<typeof assessmentSchema>;

// Enhanced result type
interface AssessmentResult {
  score: number;
  riskGroup: {
    group: string;
    risk: string;
    color: string;
  };
  patientId: string;
  email: string;
  firstName: string;
  lastName: string;
  apiRiskGroup: string;
}

// Risk group assignment
const getRiskGroup = (score: number) => {
  if (score < 7) return { group: 'A', risk: 'Low Risk', color: 'default' };
  if (score < 12) return { group: 'B', risk: 'Slightly Elevated Risk', color: 'secondary' };
  if (score < 15) return { group: 'C', risk: 'Moderate Risk', color: 'destructive' };
  return { group: 'D', risk: 'High Risk', color: 'destructive' };
};

export default function AssessmentPage() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [result, setResult] = useState<AssessmentResult | null>(null);

  const form = useForm<AssessmentForm>({
    resolver: zodResolver(assessmentSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      age: '',
      bmi: '',
      waistCircumference: '',
      physicalActivity: '',
      vegetableConsumption: '',
      bloodPressureMedication: '',
      highGlucoseHistory: '',
      familyDiabetesHistory: '',
      notes: '',
    },
  });

  const onSubmit = async (data: AssessmentForm) => {
    setIsSubmitting(true);

    try {
      // Submit assessment to backend
      const response = await assessmentApi.create({
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        phone: data.phone,
        age: data.age,
        bmi: data.bmi,
        waistCircumference: data.waistCircumference,
        physicalActivity: data.physicalActivity,
        vegetableConsumption: data.vegetableConsumption,
        bloodPressureMedication: data.bloodPressureMedication,
        highGlucoseHistory: data.highGlucoseHistory,
        familyDiabetesHistory: data.familyDiabetesHistory,
        notes: data.notes,
        assessedBy: 'Dr. Admin', // You can make this dynamic
      });

      if (response.success) {
        const { riskScore, riskGroup } = response.data;
        const riskGroupInfo = getRiskGroup(riskScore);

        setResult({
          score: riskScore,
          riskGroup: riskGroupInfo,
          patientId: response.data.assessment.patientId,
          email: data.email,
          firstName: data.firstName,
          lastName: data.lastName,
          apiRiskGroup: riskGroup
        });

        toast.success('Assessment completed and saved successfully!');
      } else {
        throw new Error('Failed to save assessment');
      }
    } catch (error) {
      console.error('Assessment error:', error);
      toast.error('Failed to complete assessment. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const sendInvitation = async () => {
    if (!result) return;

    try {
      const response = await invitationApi.send({
        patientId: (result as any).patientId,
        email: (result as any).email,
        firstName: (result as any).firstName,
        lastName: (result as any).lastName,
        riskGroup: (result as any).apiRiskGroup,
        riskScore: result.score,
      });

      if (response.success) {
        toast.success(`Invitation sent to ${(result as any).email}!`);
      } else {
        throw new Error('Failed to send invitation');
      }
    } catch (error) {
      console.error('Invitation error:', error);
      toast.error('Failed to send invitation. Please try again.');
    }
  };

  if (result) {
    const getRiskIcon = (group: string) => {
      switch (group) {
        case 'A': return <CheckCircle className="h-12 w-12 text-green-500" />;
        case 'B': return <Info className="h-12 w-12 text-blue-500" />;
        case 'C': return <AlertTriangle className="h-12 w-12 text-orange-500" />;
        case 'D': return <AlertTriangle className="h-12 w-12 text-red-500" />;
        default: return <Info className="h-12 w-12 text-gray-500" />;
      }
    };

    const getRiskGradient = (group: string) => {
      switch (group) {
        case 'A': return 'from-green-600 via-emerald-600 to-teal-700';
        case 'B': return 'from-blue-600 via-indigo-600 to-purple-700';
        case 'C': return 'from-orange-600 via-amber-600 to-yellow-700';
        case 'D': return 'from-red-600 via-rose-600 to-pink-700';
        default: return 'from-gray-600 via-slate-600 to-zinc-700';
      }
    };

    const getRecommendations = (group: string) => {
      switch (group) {
        case 'A':
          return [
            'Continue maintaining healthy lifestyle habits',
            'Regular physical activity (150 minutes/week)',
            'Balanced diet with plenty of vegetables',
            'Annual health check-ups'
          ];
        case 'B':
          return [
            'Increase physical activity to 30+ minutes daily',
            'Focus on weight management if overweight',
            'Reduce refined carbohydrates and sugars',
            'Monitor blood pressure regularly',
            'Consider annual glucose screening'
          ];
        case 'C':
          return [
            'Structured lifestyle intervention program',
            'Weight loss of 5-10% if overweight',
            'Intensive dietary counseling',
            'Regular glucose tolerance testing',
            'Consider diabetes prevention program'
          ];
        case 'D':
          return [
            'Immediate lifestyle intervention required',
            'Urgent glucose tolerance test',
            'Comprehensive diabetes prevention program',
            'Regular monitoring by healthcare provider',
            'Consider medication if lifestyle changes insufficient'
          ];
        default:
          return [];
      }
    };

    return (
      <div className="space-y-8">
        {/* Enhanced Header with Gradient - matching dashboard style */}
        <div className={`relative overflow-hidden rounded-2xl bg-gradient-to-br ${getRiskGradient(result.riskGroup.group)} p-8 text-white`}>
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center space-x-4 mb-4">
                  <Button variant="ghost" size="sm" asChild className="text-white hover:bg-white/20">
                    <Link href="/dashboard">
                      <ArrowLeft className="mr-2 h-4 w-4" />
                      Back to Dashboard
                    </Link>
                  </Button>
                  <Badge variant="outline" className="bg-white/20 text-white border-white/30">
                    Assessment Complete
                  </Badge>
                </div>
                <h1 className="text-4xl font-bold mb-2">Assessment Results</h1>
                <p className="text-white/90 text-lg">
                  Finnish Diabetes Risk Score for {result.firstName} {result.lastName}
                </p>
                <div className="flex items-center mt-4 space-x-6">
                  <div className="flex items-center space-x-2">
                    <User className="h-5 w-5 text-white/80" />
                    <span className="text-white/90">{result.email}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Calculator className="h-5 w-5 text-white/80" />
                    <span className="text-white/90">Score: {result.score}/26</span>
                  </div>
                </div>
              </div>
              <div className="hidden lg:block">
                <div className="w-32 h-32 rounded-full bg-white/10 flex items-center justify-center">
                  {getRiskIcon(result.riskGroup.group)}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Results Cards Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Risk Score Card */}
          <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100 hover:shadow-xl transition-all duration-300">
            <CardHeader className="text-center pb-4">
              <div className="flex justify-center mb-4">
                {getRiskIcon(result.riskGroup.group)}
              </div>
              <CardTitle className="text-3xl font-bold text-gray-800">
                {result.score}/26
              </CardTitle>
              <CardDescription>
                Risk Score
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <Badge
                variant={result.riskGroup.color as any}
                className="text-lg px-4 py-2 font-semibold"
              >
                Group {result.riskGroup.group}
              </Badge>
              <p className="text-sm text-gray-600 mt-2">
                {result.riskGroup.risk}
              </p>
            </CardContent>
          </Card>

          {/* Risk Assessment Card */}
          <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Target className="mr-2 h-5 w-5 text-purple-600" />
                Risk Assessment
              </CardTitle>
              <CardDescription>
                10-year diabetes development probability
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Alert className="border-0 bg-gray-50">
                <AlertDescription className="text-sm leading-relaxed">
                  {result.riskGroup.group === 'A' &&
                    'Excellent! You have a low risk (1%) of developing type 2 diabetes in the next 10 years.'
                  }
                  {result.riskGroup.group === 'B' &&
                    'You have a slightly elevated risk (4%) of developing type 2 diabetes.'
                  }
                  {result.riskGroup.group === 'C' &&
                    'You have a moderate risk (17%) of developing type 2 diabetes.'
                  }
                  {result.riskGroup.group === 'D' &&
                    'You have a high risk (33%) of developing type 2 diabetes.'
                  }
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>

          {/* Action Card */}
          <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100 hover:shadow-xl transition-all duration-300">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Mail className="mr-2 h-5 w-5 text-green-600" />
                Next Steps
              </CardTitle>
              <CardDescription>
                Send invitation and start intervention
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button
                onClick={sendInvitation}
                size="lg"
                className="w-full bg-green-600 hover:bg-green-700 text-white"
              >
                <Mail className="mr-2 h-5 w-5" />
                Send App Invitation
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => setResult(null)}
                className="w-full"
              >
                <User className="mr-2 h-5 w-5" />
                New Assessment
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Recommendations Card */}
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Heart className="mr-2 h-5 w-5 text-red-500" />
              Clinical Recommendations
            </CardTitle>
            <CardDescription>
              Evidence-based interventions for Group {result.riskGroup.group} patients
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-3 flex items-center">
                  <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                  Recommended Actions
                </h4>
                <ul className="space-y-2">
                  {getRecommendations(result.riskGroup.group).map((rec) => (
                    <li key={rec} className="flex items-start space-x-2 text-sm">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                      <span>{rec}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold mb-3 flex items-center">
                  <Stethoscope className="mr-2 h-4 w-4 text-blue-500" />
                  Follow-up Schedule
                </h4>
                <div className="space-y-2 text-sm">
                  {result.riskGroup.group === 'A' && (
                    <>
                      <p>• Annual health assessment</p>
                      <p>• Lifestyle counseling as needed</p>
                      <p>• Glucose screening every 3 years</p>
                    </>
                  )}
                  {result.riskGroup.group === 'B' && (
                    <>
                      <p>• 6-month follow-up appointment</p>
                      <p>• Annual glucose screening</p>
                      <p>• Lifestyle intervention program</p>
                    </>
                  )}
                  {result.riskGroup.group === 'C' && (
                    <>
                      <p>• 3-month follow-up appointment</p>
                      <p>• Glucose tolerance test within 1 month</p>
                      <p>• Structured lifestyle program enrollment</p>
                    </>
                  )}
                  {result.riskGroup.group === 'D' && (
                    <>
                      <p>• Immediate follow-up (within 2 weeks)</p>
                      <p>• Urgent glucose tolerance test</p>
                      <p>• Intensive intervention program</p>
                      <p>• Consider pharmacological intervention</p>
                    </>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Enhanced Header with Gradient - matching dashboard style */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 p-8 text-white">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative z-10">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-4 mb-4">
                <Button variant="ghost" size="sm" asChild className="text-white hover:bg-white/20">
                  <Link href="/dashboard">
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back to Dashboard
                  </Link>
                </Button>
              </div>
              <h1 className="text-4xl font-bold mb-2">New Patient Assessment</h1>
              <p className="text-blue-100 text-lg">
                Finnish Diabetes Risk Score Assessment - Complete the questionnaire to determine prediabetes risk
              </p>
              <div className="flex items-center mt-4 space-x-6">
                <div className="flex items-center space-x-2">
                  <Calculator className="h-5 w-5 text-blue-200" />
                  <span className="text-blue-100">8-question assessment</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Target className="h-5 w-5 text-blue-200" />
                  <span className="text-blue-100">Evidence-based scoring</span>
                </div>
              </div>
            </div>
            <div className="hidden lg:block">
              <div className="w-32 h-32 rounded-full bg-white/10 flex items-center justify-center">
                <User className="h-16 w-16 text-white/80" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Assessment Form */}
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center">
            <User className="mr-2 h-5 w-5 text-blue-600" />
            Patient Information & Risk Assessment
          </CardTitle>
          <CardDescription>
            Please complete all sections for accurate risk calculation
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              {/* Patient Information Section */}
              <Card className="border-0 shadow-sm bg-gradient-to-br from-blue-50 to-blue-100">
                <CardHeader className="pb-4">
                  <CardTitle className="text-lg flex items-center">
                    <User className="mr-2 h-5 w-5 text-blue-600" />
                    Patient Information
                  </CardTitle>
                  <CardDescription>
                    Basic patient details for assessment record
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="firstName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>First Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter first name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="lastName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Last Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter last name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email Address</FormLabel>
                        <FormControl>
                          <Input type="email" placeholder="Enter email address" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone Number</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter phone number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  </div>
                </CardContent>
              </Card>

              {/* Finnish Diabetes Risk Score Questions */}
              <Card className="border-0 shadow-sm bg-gradient-to-br from-purple-50 to-purple-100">
                <CardHeader className="pb-4">
                  <CardTitle className="text-lg flex items-center">
                    <Calculator className="mr-2 h-5 w-5 text-purple-600" />
                    Risk Assessment Questions
                  </CardTitle>
                  <CardDescription>
                    Finnish Diabetes Risk Score (FINDRISC) - 8 evidence-based questions
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">

                {/* Age */}
                <FormField
                  control={form.control}
                  name="age"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>1. Age</FormLabel>
                      <FormControl>
                        <RadioGroup onValueChange={field.onChange} value={field.value}>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="under-45" id="age-under-45" />
                            <label htmlFor="age-under-45">Under 45 years (0 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="45-54" id="age-45-54" />
                            <label htmlFor="age-45-54">45-54 years (2 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="55-64" id="age-55-64" />
                            <label htmlFor="age-55-64">55-64 years (3 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="65+" id="age-65-plus" />
                            <label htmlFor="age-65-plus">Over 64 years (4 points)</label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* BMI */}
                <FormField
                  control={form.control}
                  name="bmi"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>2. Body Mass Index (BMI)</FormLabel>
                      <FormControl>
                        <RadioGroup onValueChange={field.onChange} value={field.value}>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="under-25" id="bmi-under-25" />
                            <label htmlFor="bmi-under-25">Lower than 25 kg/m² (0 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="25-30" id="bmi-25-30" />
                            <label htmlFor="bmi-25-30">25-30 kg/m² (1 point)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="30+" id="bmi-30-plus" />
                            <label htmlFor="bmi-30-plus">Higher than 30 kg/m² (3 points)</label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Waist Circumference */}
                <FormField
                  control={form.control}
                  name="waistCircumference"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>3. Waist Circumference</FormLabel>
                      <FormControl>
                        <RadioGroup onValueChange={field.onChange} value={field.value}>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="male-under-94" id="waist-male-under-94" />
                            <label htmlFor="waist-male-under-94">Men: Less than 94 cm (0 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="female-under-80" id="waist-female-under-80" />
                            <label htmlFor="waist-female-under-80">Women: Less than 80 cm (0 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="male-94-102" id="waist-male-94-102" />
                            <label htmlFor="waist-male-94-102">Men: 94-102 cm (3 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="female-80-88" id="waist-female-80-88" />
                            <label htmlFor="waist-female-80-88">Women: 80-88 cm (3 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="male-102+" id="waist-male-102-plus" />
                            <label htmlFor="waist-male-102-plus">Men: More than 102 cm (4 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="female-88+" id="waist-female-88-plus" />
                            <label htmlFor="waist-female-88-plus">Women: More than 88 cm (4 points)</label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Physical Activity */}
                <FormField
                  control={form.control}
                  name="physicalActivity"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>4. Do you usually have at least 30 minutes of physical activity at work and/or during leisure time?</FormLabel>
                      <FormControl>
                        <RadioGroup onValueChange={field.onChange} value={field.value}>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="yes" id="activity-yes" />
                            <label htmlFor="activity-yes">Yes (0 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="no" id="activity-no" />
                            <label htmlFor="activity-no">No (2 points)</label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Vegetable Consumption */}
                <FormField
                  control={form.control}
                  name="vegetableConsumption"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>5. How often do you eat vegetables, fruit or berries?</FormLabel>
                      <FormControl>
                        <RadioGroup onValueChange={field.onChange} value={field.value}>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="yes" id="vegetables-yes" />
                            <label htmlFor="vegetables-yes">Every day (0 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="no" id="vegetables-no" />
                            <label htmlFor="vegetables-no">Not every day (1 point)</label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Blood Pressure Medication */}
                <FormField
                  control={form.control}
                  name="bloodPressureMedication"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>6. Have you ever taken medication for high blood pressure?</FormLabel>
                      <FormControl>
                        <RadioGroup onValueChange={field.onChange} value={field.value}>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="no" id="bp-med-no" />
                            <label htmlFor="bp-med-no">No (0 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="yes" id="bp-med-yes" />
                            <label htmlFor="bp-med-yes">Yes (2 points)</label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* High Glucose History */}
                <FormField
                  control={form.control}
                  name="highGlucoseHistory"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>7. Have you ever been found to have high blood glucose?</FormLabel>
                      <FormControl>
                        <RadioGroup onValueChange={field.onChange} value={field.value}>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="no" id="glucose-no" />
                            <label htmlFor="glucose-no">No (0 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="yes" id="glucose-yes" />
                            <label htmlFor="glucose-yes">Yes (5 points)</label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Family Diabetes History */}
                <FormField
                  control={form.control}
                  name="familyDiabetesHistory"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>8. Have any of your family members been diagnosed with diabetes?</FormLabel>
                      <FormControl>
                        <RadioGroup onValueChange={field.onChange} value={field.value}>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="no" id="family-diabetes-no" />
                            <label htmlFor="family-diabetes-no">No (0 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="yes-grandparent-aunt-uncle" id="family-diabetes-distant" />
                            <label htmlFor="family-diabetes-distant">Yes: grandparent, aunt, uncle, or first cousin (3 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="yes-parent-sibling-child" id="family-diabetes-close" />
                            <label htmlFor="family-diabetes-close">Yes: parent, brother, sister, or own child (5 points)</label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Additional Notes */}
                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Additional Notes (Optional)</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Any additional observations or notes about the patient..."
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex justify-end pt-6 border-t border-purple-200">
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    size="lg"
                    className="bg-purple-600 hover:bg-purple-700 text-white px-8"
                  >
                    <Calculator className="mr-2 h-5 w-5" />
                    {isSubmitting ? 'Calculating...' : 'Calculate Risk Score'}
                  </Button>
                </div>
                </CardContent>
              </Card>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
